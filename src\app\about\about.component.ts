import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [RouterModule, CommonModule, ReactiveFormsModule],
  templateUrl: './about.html',
  styleUrl: './about.css'
})
export class AboutComponent implements OnInit, OnDestroy, AfterViewInit {

  // Mobile navigation state
  isMobileMenuOpen = false;

  // Form state
  contactForm!: FormGroup;
  showPassword = false;
  isSubmitting = false;
  showSuccessMessage = false;

  // Intersection observer for animations
  private observer?: IntersectionObserver;

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit(): void {
    // Initialize reactive form with validation
    this.contactForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      message: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  ngAfterViewInit(): void {
    // Set up scroll animations after view is initialized
    this.setupScrollAnimations();
    this.setupSmoothScrolling();
  }

  ngOnDestroy(): void {
    // Clean up
    document.body.style.overflow = '';
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  // Toggle mobile menu
  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;

    // Prevent body scroll when menu is open
    if (this.isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  // Close mobile menu
  closeMobileMenu(): void {
    this.isMobileMenuOpen = false;
    document.body.style.overflow = '';
  }

  // Toggle password visibility (starts hidden as per user preference)
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  // Handle contact form submission
  onSubmitContact(): void {
    if (this.contactForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      // Simulate API call
      setTimeout(() => {
        this.isSubmitting = false;
        this.showSuccessMessage = true;
        this.contactForm.reset();

        // Hide success message after 5 seconds
        setTimeout(() => {
          this.showSuccessMessage = false;
        }, 5000);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.contactForm.controls).forEach(key => {
        this.contactForm.get(key)?.markAsTouched();
      });
    }
  }

  // Set up scroll animations using Intersection Observer
  private setupScrollAnimations(): void {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');

          // Trigger counter animation for stats
          if (entry.target.classList.contains('stats-section')) {
            this.animateCounters();
          }
        }
      });
    }, observerOptions);

    // Observe elements for animation
    const elementsToObserve = [
      '.feature-card',
      '.timeline-item',
      '.stats-section',
      '.testimonial-card',
      '.leader-card',
      '.campus-item'
    ];

    elementsToObserve.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (this.observer) {
          this.observer.observe(element);
        }
      });
    });
  }

  // Animate counters
  private animateCounters(): void {
    const counters = document.querySelectorAll('.stat-number[data-target]');

    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-target') || '0');
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;

      const updateCounter = () => {
        if (current < target) {
          current += increment;
          counter.textContent = Math.floor(current).toLocaleString() + '+';
          requestAnimationFrame(updateCounter);
        } else {
          counter.textContent = target.toLocaleString() + '+';
        }
      };

      updateCounter();
    });
  }

  // Set up smooth scrolling for anchor links
  private setupSmoothScrolling(): void {
    const anchors = document.querySelectorAll('a[href^="#"]');

    anchors.forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();

        const targetId = anchor.getAttribute('href');
        const target = document.querySelector(targetId || '');

        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }
}
