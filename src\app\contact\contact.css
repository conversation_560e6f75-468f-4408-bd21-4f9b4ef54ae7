/* Header Navigation Styles - consistent with other pages */
/* Navigation group positioning and hover effects */
header .group {
  position: relative;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

header .group:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* SVG icons styling */
header .group svg {
  stroke: #ffffff;
  transition: all 0.3s ease;
}

header .group:hover svg {
  stroke: #fb923c;
  transform: scale(1.1);
}

/* Text labels - positioned to show when header expands */
header .group span {
  position: absolute;
  top: 40px; /* Position below the icon */
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

/* Show text when hovering */
header .group:hover span {
  opacity: 1;
}

/* Header smooth expansion - NO SCROLLBAR */
header {
  transition: height 0.3s ease, padding 0.3s ease;
  overflow: hidden !important; /* Remove scrollbar completely */
  height: 80px; /* A bit more height normally */
  padding-bottom: 8px; /* A little bit more padding bottom normally */
}

/* Header expands when hovering navigation */
header:has(.group:hover) {
  height: 110px !important; /* Expand a little bit more to show text */
  padding-bottom: 10px;
}

/* Mobile burger menu - hidden by default */
#mobile-burger {
  display: none;
}

/* Mobile: Show burger menu, navigation slides in/out */
@media (max-width: 768px) {
  #mobile-burger {
    display: flex !important;
  }

  /* Navigation links - hidden by default, slide in from right */
  #nav-links {
    position: fixed;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.96) 0%, rgba(30, 41, 59, 0.96) 100%);
    backdrop-filter: blur(20px);
    width: 50vw;
    height: 100vh;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 2rem 1rem;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 10000;
    overflow-y: auto;
  }

  /* Show navigation when mobile menu is open */
  #nav-links.mobile-open {
    transform: translateX(0);
  }

  /* Disable header expansion on mobile */
  header:has(.group:hover) {
    height: 80px !important;
    padding-bottom: 8px !important;
  }

  /* Hide text labels on mobile */
  header .group span {
    display: none !important;
  }

  /* Style navigation links for mobile - clean list style */
  #nav-links .group {
    width: 100% !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 1.5rem 1rem !important;
    margin-bottom: 0.5rem !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 0 !important;
    background: none !important;
    text-align: center !important;
    display: flex !important;
    position: relative !important;
  }

  #nav-links .group:hover {
    background: rgba(255, 255, 255, 0.05) !important;
    border-bottom-color: rgba(96, 165, 250, 0.5) !important;
    transform: translateX(8px) !important;
  }

  #nav-links .group:active {
    transform: scale(0.98) !important;
  }

  #nav-links .group span {
    display: block !important;
    position: static !important;
    opacity: 1 !important;
    background: none !important;
    color: #e2e8f0 !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    padding: 0 !important;
    margin: 0 !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    border-radius: 0 !important;
    white-space: nowrap !important;
  }

  #nav-links .group:hover span {
    color: #ffffff !important;
    font-weight: 500 !important;
    opacity: 1 !important;
  }

  /* Last item - no border */
  #nav-links .group:last-child {
    border-bottom: none;
  }
}

/* Mobile navigation header - hidden by default */
.mobile-nav-header {
  display: none;
}

/* Show mobile header only when navigation is open */
#nav-links.mobile-open .mobile-nav-header {
  display: block !important;
  animation: fadeInUp 0.5s ease-out;
}

/* Animate navigation items when modal opens */
#nav-links.mobile-open .group {
  animation: slideInRight 0.4s ease-out;
  animation-fill-mode: both;
}

/* Stagger animation for each navigation item */
#nav-links.mobile-open .group:nth-child(2) { animation-delay: 0.1s; }
#nav-links.mobile-open .group:nth-child(3) { animation-delay: 0.15s; }
#nav-links.mobile-open .group:nth-child(4) { animation-delay: 0.2s; }
#nav-links.mobile-open .group:nth-child(5) { animation-delay: 0.25s; }
#nav-links.mobile-open .group:nth-child(6) { animation-delay: 0.3s; }

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Contact page specific styles */
.contact-container {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 14px;
  margin-top: 4px;
}

.submit-btn {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  padding: 14px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
}

.contact-info-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.contact-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.contact-info-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.contact-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
  color: #f97316;
}

@media (max-width: 768px) {
  .contact-form,
  .contact-info-card {
    padding: 1.5rem;
  }
  
  .form-input {
    padding: 10px 14px;
  }
  
  .submit-btn {
    padding: 12px 24px;
  }
}
