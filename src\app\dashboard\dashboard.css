/* Modern Interactive Library Management Dashboard Styles */

/* Dark Mode Support */
[data-theme="dark"] .nav-link {
  color: #9ca3af;
}

[data-theme="dark"] .nav-link:hover {
  color: #f9fafb;
  background-color: #374151;
}

[data-theme="dark"] .nav-link.active {
  color: #60a5fa;
  background-color: #1e3a8a;
  border-right: 2px solid #60a5fa;
}

/* Navigation Styles */
.nav-link {
  color: #6b7280;
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: #111827;
  background-color: #f3f4f6;
  transform: translateX(4px);
}

.nav-link.active {
  color: #2563eb;
  background-color: #eff6ff;
  border-right: 2px solid #2563eb;
}

/* Logout Button Animation */
.logout-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Widget Animations */
.widget-card {
  transition: all 0.3s ease;
}

.widget-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-4px);
}

/* Weather Widget Animations */
#weather-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Stats Counter Animation */
.stat-counter {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* News Item Hover Effects */
.news-item:hover {
  transform: translateX(8px);
}

/* Sidebar fixed height and layout */
aside.w-64 {
  height: 100vh;
  position: sticky;
  top: 0;
}

/* Mobile-only responsive design */
@media (max-width: 1023px) {
  /* Hide the mobile menu button on desktop */
  .lg\:hidden {
    display: block;
  }

  /* Make sidebar mobile-friendly */
  aside.w-64 {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  /* Show sidebar when mobile menu is open */
  aside.w-64[style*="translateX(0)"] {
    transform: translateX(0) !important;
  }

  /* Hide right sidebar on mobile */
  aside.w-80 {
    display: none;
  }

  /* Adjust main content padding for mobile */
  main {
    padding: 1rem !important;
  }

  /* Ensure header content doesn't overflow */
  header {
    padding-left: 4rem !important; /* Account for mobile menu button */
    padding-right: 1rem !important;
  }
}

/* Desktop - keep everything as original */
@media (min-width: 1024px) {
  /* Hide mobile menu button on desktop */
  .lg\:hidden {
    display: none;
  }

  /* Ensure sidebar is visible on desktop */
  aside.w-64 {
    position: sticky;
    top: 0;
    height: 100vh;
    transform: translateX(0) !important;
  }

  /* Show right sidebar on desktop */
  aside.w-80 {
    display: block;
  }

  /* Hide mobile widgets on desktop */
  .lg\:hidden {
    display: none !important;
  }
}

/* Notification Badge Animation */
.notification-badge {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Smooth Scrolling */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Dark Mode Scrollbar */
[data-theme="dark"] .overflow-y-auto {
  scrollbar-color: #4b5563 #1f2937;
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-track {
  background: #1f2937;
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #4b5563;
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Dark Mode Toggle Animation */
.dark-mode-toggle {
  transition: transform 0.2s ease;
}

.dark-mode-toggle:hover {
  transform: scale(1.1);
}

/* Mobile Menu Improvements */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
  transition: opacity 0.3s ease;
}

/* Card Hover Effects */
.widget-card:hover {
  transform: translateY(-2px);
}

/* Responsive Text Scaling */
@media (max-width: 640px) {
  .text-3xl {
    font-size: 1.875rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .text-xl {
    font-size: 1.125rem;
  }
}

/* Enhanced Focus States */
.nav-link:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

[data-theme="dark"] .nav-link:focus,
[data-theme="dark"] button:focus {
  outline-color: #60a5fa;
}

/* Loading States */
.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Improved Transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Mobile Header Improvements */
@media (max-width: 1023px) {
  /* Ensure header text doesn't get cut off */
  .header-title {
    font-size: 1.25rem !important;
    line-height: 1.75rem !important;
  }

  /* Mobile button spacing */
  .header-buttons {
    gap: 0.5rem !important;
  }

  /* Ensure mobile widgets are properly spaced */
  .mobile-widgets {
    margin-top: 2rem;
  }
}

/* Ensure proper text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* AI Chat Widget Styles */
.chat-widget {
  z-index: 1000;
}

/* Chat button pulse animation */
.chat-button-pulse {
  animation: chatPulse 2s infinite;
}

@keyframes chatPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Chat window animations */
.chat-window-enter {
  animation: chatWindowSlideIn 0.3s ease-out;
}

@keyframes chatWindowSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Chat message animations */
.chat-message {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typing indicator animation */
.typing-indicator {
  animation: typingPulse 1.5s infinite;
}

@keyframes typingPulse {
  0%, 60%, 100% {
    opacity: 0.4;
  }
  30% {
    opacity: 1;
  }
}

/* Chat scrollbar styling */
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Dark mode chat scrollbar */
[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb {
  background: #4b5563;
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Tooltip animation */
.tooltip-enter {
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chat input focus styles */
.chat-input:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* Mobile chat widget adjustments */
@media (max-width: 640px) {
  .chat-widget {
    bottom: 1rem;
    right: 1rem;
  }

  .chat-window {
    width: calc(100vw - 2rem);
    max-width: 320px;
    height: 400px;
  }

  .chat-tooltip {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Chat button hover effects */
.chat-button:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* Message bubble styling */
.message-bubble {
  max-width: 85%;
  word-wrap: break-word;
}

.message-bubble.user {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.message-bubble.ai {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
}

[data-theme="dark"] .message-bubble.ai {
  background: #374151;
  border-color: #4b5563;
}

/* Online indicator pulse */
.online-indicator {
  animation: onlinePulse 2s infinite;
}

@keyframes onlinePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Chat header gradient */
.chat-header {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

[data-theme="dark"] .chat-header {
  background: linear-gradient(135deg, #1f2937, #111827);
}

/* Smooth transitions for all chat elements */
.chat-widget * {
  transition: all 0.2s ease;
}

/* Notification badge bounce */
.notification-badge {
  animation: badgeBounce 0.6s ease-in-out infinite alternate;
}

@keyframes badgeBounce {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.1);
  }
}